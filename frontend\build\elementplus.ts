/**
 * Element Plus 优化配置
 * 解决 Vite 依赖预构建频繁重载问题
 */

// Element Plus 常用组件样式列表
export const elementPlusStyleIncludes = [
  // 基础组件
  "element-plus/es/components/button/style/css",
  "element-plus/es/components/input/style/css",
  "element-plus/es/components/icon/style/css",
  "element-plus/es/components/tooltip/style/css",
  "element-plus/es/components/loading/style/css",
  "element-plus/es/components/message/style/css",
  
  // 布局组件
  "element-plus/es/components/container/style/css",
  "element-plus/es/components/header/style/css",
  "element-plus/es/components/aside/style/css",
  "element-plus/es/components/main/style/css",
  "element-plus/es/components/footer/style/css",
  "element-plus/es/components/row/style/css",
  "element-plus/es/components/col/style/css",
  "element-plus/es/components/space/style/css",
  "element-plus/es/components/divider/style/css",
  
  // 表单组件
  "element-plus/es/components/form/style/css",
  "element-plus/es/components/form-item/style/css",
  "element-plus/es/components/select/style/css",
  "element-plus/es/components/option/style/css",
  "element-plus/es/components/input-number/style/css",
  "element-plus/es/components/radio/style/css",
  "element-plus/es/components/checkbox/style/css",
  "element-plus/es/components/switch/style/css",
  "element-plus/es/components/date-picker/style/css",
  "element-plus/es/components/color-picker/style/css",
  
  // 数据展示
  "element-plus/es/components/table/style/css",
  "element-plus/es/components/table-column/style/css",
  "element-plus/es/components/pagination/style/css",
  "element-plus/es/components/tag/style/css",
  "element-plus/es/components/progress/style/css",
  "element-plus/es/components/tree/style/css",
  "element-plus/es/components/descriptions/style/css",
  "element-plus/es/components/descriptions-item/style/css",
  "element-plus/es/components/empty/style/css",
  "element-plus/es/components/card/style/css",
  
  // 导航组件
  "element-plus/es/components/menu/style/css",
  "element-plus/es/components/menu-item/style/css",
  "element-plus/es/components/sub-menu/style/css",
  "element-plus/es/components/breadcrumb/style/css",
  "element-plus/es/components/breadcrumb-item/style/css",
  "element-plus/es/components/tabs/style/css",
  "element-plus/es/components/tab-pane/style/css",
  "element-plus/es/components/dropdown/style/css",
  "element-plus/es/components/dropdown-menu/style/css",
  "element-plus/es/components/dropdown-item/style/css",
  
  // 反馈组件
  "element-plus/es/components/dialog/style/css",
  "element-plus/es/components/drawer/style/css",
  "element-plus/es/components/popover/style/css",
  "element-plus/es/components/message-box/style/css",
  
  // 其他组件
  "element-plus/es/components/scrollbar/style/css",
  "element-plus/es/components/badge/style/css",
  "element-plus/es/components/watermark/style/css",
  "element-plus/es/components/text/style/css",
  "element-plus/es/components/base/style/css"
];

// Element Plus 优化配置
export const elementPlusOptimizeConfig = {
  // 预构建包含的依赖
  include: [
    "element-plus",
    "element-plus/es",
    ...elementPlusStyleIncludes
  ],
  
  // 排除的依赖（让 Vite 动态处理）
  exclude: [
    // 排除所有样式文件的通配符匹配，让 resolver 处理
    "element-plus/es/components/*/style/*"
  ]
};
