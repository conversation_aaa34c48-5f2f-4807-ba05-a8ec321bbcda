/**
 * Element Plus 样式按需导入
 * 手动管理样式导入，避免 Vite 频繁重新优化
 */

// Element Plus 基础样式
@import "element-plus/theme-chalk/base.css";

// 常用组件样式 - 根据项目实际使用情况调整
@import "element-plus/theme-chalk/el-button.css";
@import "element-plus/theme-chalk/el-input.css";
@import "element-plus/theme-chalk/el-icon.css";
@import "element-plus/theme-chalk/el-tooltip.css";
@import "element-plus/theme-chalk/el-loading.css";
@import "element-plus/theme-chalk/el-message.css";

// 布局组件
@import "element-plus/theme-chalk/el-container.css";
@import "element-plus/theme-chalk/el-header.css";
@import "element-plus/theme-chalk/el-aside.css";
@import "element-plus/theme-chalk/el-main.css";
@import "element-plus/theme-chalk/el-footer.css";
@import "element-plus/theme-chalk/el-row.css";
@import "element-plus/theme-chalk/el-col.css";
@import "element-plus/theme-chalk/el-space.css";
@import "element-plus/theme-chalk/el-divider.css";

// 表单组件
@import "element-plus/theme-chalk/el-form.css";
@import "element-plus/theme-chalk/el-form-item.css";
@import "element-plus/theme-chalk/el-select.css";
@import "element-plus/theme-chalk/el-option.css";
@import "element-plus/theme-chalk/el-input-number.css";
@import "element-plus/theme-chalk/el-radio.css";
@import "element-plus/theme-chalk/el-checkbox.css";
@import "element-plus/theme-chalk/el-switch.css";
@import "element-plus/theme-chalk/el-date-picker.css";
@import "element-plus/theme-chalk/el-color-picker.css";

// 数据展示
@import "element-plus/theme-chalk/el-table.css";
@import "element-plus/theme-chalk/el-pagination.css";
@import "element-plus/theme-chalk/el-tag.css";
@import "element-plus/theme-chalk/el-progress.css";
@import "element-plus/theme-chalk/el-tree.css";
@import "element-plus/theme-chalk/el-descriptions.css";
@import "element-plus/theme-chalk/el-empty.css";
@import "element-plus/theme-chalk/el-card.css";

// 导航组件
@import "element-plus/theme-chalk/el-menu.css";
@import "element-plus/theme-chalk/el-breadcrumb.css";
@import "element-plus/theme-chalk/el-tabs.css";
@import "element-plus/theme-chalk/el-dropdown.css";

// 反馈组件
@import "element-plus/theme-chalk/el-dialog.css";
@import "element-plus/theme-chalk/el-drawer.css";
@import "element-plus/theme-chalk/el-popover.css";
@import "element-plus/theme-chalk/el-message-box.css";

// 其他组件
@import "element-plus/theme-chalk/el-scrollbar.css";
@import "element-plus/theme-chalk/el-badge.css";
@import "element-plus/theme-chalk/el-watermark.css";
@import "element-plus/theme-chalk/el-text.css";
